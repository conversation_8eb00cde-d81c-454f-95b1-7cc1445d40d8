# 变量引用同步功能测试指南

## 测试目标

验证当源头变量发生变化时，关联的变量引用能够正确同步更新，包括：

1. **源头变量 key 修改后**，关联的变量引用能检测到错误
2. **源头变量 type 类型修改后**，关联的变量 dataType 能同步修改
3. **源头变量 description 描述修改后**，关联的变量 variableName 能同步修改
4. **源头变量删除后**，关联的变量引用能显示错误状态

## 测试步骤

### 准备工作

1. 启动项目：`npm run dev`
2. 打开浏览器访问：http://localhost:3334/
3. 进入工作流设计页面或任何包含变量配置的页面

### 测试场景 1：变量 key 修改测试

**步骤：**
1. 创建一个源头变量，例如：
   - key: `5530`
   - type: `string`
   - description: `条码`

2. 在另一个地方创建一个变量引用，选择上面创建的变量

3. 修改源头变量的 key 为 `5530111`

**预期结果：**
- 关联的变量引用应该显示错误状态
- 显示文本应该包含 "(变量不存在)" 的提示
- 变量引用应该有红色的错误样式

### 测试场景 2：变量 type 修改测试

**步骤：**
1. 创建一个源头变量：
   - key: `testVar`
   - type: `string`
   - description: `测试变量`

2. 创建变量引用，选择该变量

3. 修改源头变量的 type 为 `number`

**预期结果：**
- 关联的变量引用的 dataType 应该自动更新为 `number`
- 类型标识应该显示为 "数字"

### 测试场景 3：变量 description 修改测试

**步骤：**
1. 创建一个源头变量：
   - key: `testDesc`
   - type: `string`
   - description: `原始描述`

2. 创建变量引用，选择该变量

3. 修改源头变量的 description 为 `新的描述`

**预期结果：**
- 关联的变量引用的 variableName 应该自动更新为 `新的描述`
- 显示文本应该显示新的描述

### 测试场景 4：变量删除测试

**步骤：**
1. 创建一个源头变量并创建引用

2. 删除源头变量

**预期结果：**
- 关联的变量引用应该显示错误状态
- 显示文本应该包含 "(变量不存在)" 的提示
- 变量引用应该有红色的错误样式和删除线

## 测试页面建议

推荐在以下页面进行测试：

1. **工作流设计页面** - 可以创建多个步骤，在不同步骤中测试变量引用
2. **API 请求配置页面** - 可以在参数配置中测试变量引用
3. **数据查询配置页面** - 可以在查询条件中测试变量引用

## 调试信息

在浏览器开发者工具的控制台中，可以查看以下调试信息：

1. 变量引用失效的警告信息
2. 变量更新的日志
3. 变量引用管理器的调试信息

可以在控制台中执行以下命令查看变量引用管理器状态：
```javascript
// 获取变量引用管理器的调试信息
window.variableReferenceManager?.getDebugInfo()
```

## 常见问题

### 问题 1：变量引用没有同步更新
**可能原因：**
- 变量引用管理器没有正确注册
- onChange 事件没有正确触发

**解决方法：**
- 检查控制台是否有错误信息
- 确认变量引用管理器的调试信息

### 问题 2：错误状态没有显示
**可能原因：**
- hasError 计算属性逻辑有问题
- CSS 样式没有正确应用

**解决方法：**
- 检查 SimpleValueInput 组件的 hasError 计算属性
- 确认错误样式是否正确应用

## 测试完成标准

所有测试场景都通过，即：
- ✅ 变量 key 修改后，引用显示错误
- ✅ 变量 type 修改后，引用类型同步更新
- ✅ 变量 description 修改后，引用名称同步更新
- ✅ 变量删除后，引用显示错误状态
