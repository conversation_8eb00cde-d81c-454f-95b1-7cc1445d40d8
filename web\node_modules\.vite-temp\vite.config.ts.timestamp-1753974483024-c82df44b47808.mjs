// vite.config.ts
import path from "node:path";
import vue from "file:///E:/Code/MES1.0/tool/scm-gcp/web/node_modules/.pnpm/@vitejs+plugin-vue@5.2.4_vi_3f633b0116b3dbf2f8e75e0824951276/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///E:/Code/MES1.0/tool/scm-gcp/web/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.2._5e373b72ea06afd20905cbfbc6982610/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import { loadEnv } from "file:///E:/Code/MES1.0/tool/scm-gcp/web/node_modules/.pnpm/vite@6.3.5_@types+node@24.1_4a5fda194b3ed930aee60af7da8bdddc/node_modules/vite/dist/node/index.js";
import { viteMockServe } from "file:///E:/Code/MES1.0/tool/scm-gcp/web/node_modules/.pnpm/vite-plugin-mock@3.0.2_esbu_1a31cb236ca31eb0b776721d108d158a/node_modules/vite-plugin-mock/dist/index.mjs";
import svgLoader from "file:///E:/Code/MES1.0/tool/scm-gcp/web/node_modules/.pnpm/vite-svg-loader@5.1.0_vue@3.5.18_typescript@5.8.3_/node_modules/vite-svg-loader/index.js";
var __vite_injected_original_dirname = "E:\\Code\\MES1.0\\tool\\scm-gcp\\web";
var CWD = process.cwd();
var vite_config_default = ({ mode }) => {
  const { VITE_BASE_URL, VITE_API_URL_PREFIX, VITE_IS_REQUEST_PROXY } = loadEnv(mode, CWD);
  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        "@": path.resolve(__vite_injected_original_dirname, "./src")
      }
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            hack: `true; @import (reference) "${path.resolve("src/style/variables.less")}";`
          },
          math: "strict",
          javascriptEnabled: true
        }
      }
    },
    plugins: [
      vue(),
      vueJsx(),
      viteMockServe({
        mockPath: "mock",
        enable: true
      }),
      svgLoader()
    ],
    server: {
      port: 3333,
      host: "0.0.0.0",
      proxy: VITE_IS_REQUEST_PROXY ? {
        [VITE_API_URL_PREFIX]: "http://127.0.0.1:6981/"
      } : {}
    }
  };
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
